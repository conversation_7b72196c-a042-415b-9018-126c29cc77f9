<!-- Polkadot API Demo - HTTP Only -->
<script setup>
import { decodeAddress } from '@polkadot/keyring'
import { TypeRegistry } from '@polkadot/types'
import { hexToU8a, stringToU8a, u8aToHex } from '@polkadot/util'
import { blake2AsHex, xxhashAsHex } from '@polkadot/util-crypto'

// 创建类型注册表
const typeRegistry = new TypeRegistry()

// 初始化运行时元数据
let runtimeMetadata = null

// 获取并设置运行时元数据
async function initializeMetadata() {
  try {
    const metadata = await makeRpcRequest('state_getMetadata')
    runtimeMetadata = typeRegistry.createType('Metadata', metadata)
    typeRegistry.setMetadata(runtimeMetadata)
    addLog('运行时元数据初始化成功', 'success')
  } catch (error) {
    addLog(`元数据初始化失败: ${error.message}`, 'warning')
  }
}

// 状态管理
const polkadotState = reactive({
  // 连接状态
  isConnected: false,
  isConnecting: false,
  connectionError: null,

  // 账户信息
  sourceAccount: '5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY',
  targetAccount: '5HpG9w8EBLe5XCrbczpwq5TSXvedjrBGCwqxK1iQ7qUsSWFc',
  balance: null,

  // 交易状态
  isTransferring: false,
  transferAmount: '*************', // 1 个币 (假设 12 位小数)
  transferStatus: null,
  transferError: null,

  // 日志
  logs: [],

  // HTTP RPC 配置
  rpcUrl: 'http://127.0.0.1:9933',
  requestId: 1,
})

// 添加日志
function addLog(message, type = 'info') {
  const timestamp = new Date().toLocaleTimeString()
  polkadotState.logs.unshift({
    id: Date.now(),
    timestamp,
    message,
    type,
  })
  // 仅在开发环境下输出控制台日志
  if (import.meta.env.DEV) {
    // eslint-disable-next-line no-console
    console.log(`[${timestamp}] ${message}`)
  }
}

// HTTP JSON-RPC 请求函数
async function makeRpcRequest(method, params = []) {
  const requestBody = {
    jsonrpc: '2.0',
    id: polkadotState.requestId++,
    method,
    params,
  }

  try {
    const response = await fetch(polkadotState.rpcUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()

    if (data.error) {
      throw new Error(
        `RPC Error ${data.error.code}: ${data.error.message}`,
      )
    }

    return data.result
  }
  catch (error) {
    addLog(`🔴 RPC 请求失败 [${method}]: ${error.message}`, 'error')
    throw error
  }
}

// 连接到链 (HTTP 方式)
async function connectToChain() {
  try {
    polkadotState.isConnecting = true
    polkadotState.connectionError = null
    addLog(`连接到 ${polkadotState.rpcUrl}`, 'info')

    // 测试连接 - 获取链信息
    const chainName = await makeRpcRequest('system_chain')
    addLog(`连接成功: ${chainName}`, 'success')

    // 获取当前区块信息
    const latestBlockHash = await makeRpcRequest('chain_getHead')
    const blockHeader = await makeRpcRequest('chain_getHeader', [
      latestBlockHash,
    ])
    if (blockHeader) {
      addLog(`当前区块: #${Number.parseInt(blockHeader.number, 16)}`, 'info')
    }

    polkadotState.isConnected = true

    // 初始化运行时元数据
    await initializeMetadata()

    // 自动查询余额
    queryBalance()
  }
  catch (error) {
    polkadotState.connectionError = error.message
    addLog(`连接失败: ${error.message}`, 'error')
  }
  finally {
    polkadotState.isConnecting = false
  }
}

// 动态计算存储前缀
function getStoragePrefix(palletName, storageName) {
  // 计算 pallet 名称的 xxhash128
  const palletHash = xxhashAsHex(stringToU8a(palletName), 128)
  // 计算 storage 名称的 xxhash128
  const storageHash = xxhashAsHex(stringToU8a(storageName), 128)
  // 组合前缀
  return palletHash + storageHash.slice(2) // 移除第二个哈希的 0x 前缀
}

// 动态计算账户存储键
function getAccountStorageKey(accountAddress) {
  try {
    // 动态计算 System.Account 的存储前缀
    const storagePrefix = getStoragePrefix('System', 'Account')

    // 解码账户地址为 32 字节的公钥
    const accountId = decodeAddress(accountAddress)

    // 计算账户 ID 的 Blake2_128 哈希（用于 Blake2_128Concat）
    const accountIdHash = blake2AsHex(accountId, 128)

    // 将账户 ID 转换为十六进制字符串
    const accountIdHex = u8aToHex(accountId)

    // Blake2_128Concat 格式：hash(key) + key
    const blake2_128Concat = accountIdHash + accountIdHex.slice(2)

    // 完整的存储键：存储前缀 + Blake2_128Concat(账户ID)
    const storageKey = storagePrefix + blake2_128Concat.slice(2)

    return storageKey
  }
  catch (error) {
    addLog(`存储键计算失败: ${error.message}`, 'error')
    throw error
  }
}

// 解析 SCALE 编码的账户数据
function parseAccountData(accountDataHex) {
  if (!accountDataHex || accountDataHex === '0x') {
    return null
  }

  try {
    // 使用官方 Polkadot 类型来解析 AccountInfo
    const accountInfo = typeRegistry.createType('AccountInfo', hexToU8a(accountDataHex))

    // 从解析后的 AccountInfo 中提取数据
    const accountInfoJson = accountInfo.toJSON()

    return {
      nonce: accountInfoJson.nonce,
      consumers: accountInfoJson.consumers,
      providers: accountInfoJson.providers,
      sufficients: accountInfoJson.sufficients,
      free: accountInfoJson.data.free.toString(),
      reserved: accountInfoJson.data.reserved.toString(),
      frozen: accountInfoJson.data.frozen.toString(),
    }
  }
  catch (error) {
    // 如果官方解析失败，回退到手动解析方式
    addLog(
      `使用官方 API 解析失败，回退到手动解析: ${error.message}`,
      'warning',
    )

    return
    try {
      // 移除 0x 前缀
      const hex = accountDataHex.slice(2)

      // AccountInfo 结构（手动解析作为备用方案）
      // 动态计算字段偏移量，避免硬编码

      // 解析小端序数据的辅助函数
      const parseU32LE = hexStr =>
        Number.parseInt(hexStr.match(/.{2}/g).reverse().join(''), 16)
      const parseU128LE = hexStr =>
        BigInt(`0x${hexStr.match(/.{2}/g).reverse().join('')}`)

      // 定义字段大小（字节）
      const FIELD_SIZES = {
        u32: 4,   // 4 bytes = 8 hex chars
        u128: 16, // 16 bytes = 32 hex chars
      }

      // 动态计算字段偏移量
      let offset = 0

      // AccountInfo 前缀字段
      const nonce = parseU32LE(hex.slice(offset * 2, (offset + FIELD_SIZES.u32) * 2))
      offset += FIELD_SIZES.u32

      const consumers = parseU32LE(hex.slice(offset * 2, (offset + FIELD_SIZES.u32) * 2))
      offset += FIELD_SIZES.u32

      const providers = parseU32LE(hex.slice(offset * 2, (offset + FIELD_SIZES.u32) * 2))
      offset += FIELD_SIZES.u32

      const sufficients = parseU32LE(hex.slice(offset * 2, (offset + FIELD_SIZES.u32) * 2))
      offset += FIELD_SIZES.u32

      // AccountData 字段（从动态计算的偏移量开始）
      const freeBalance = parseU128LE(
        hex.slice(offset * 2, (offset + FIELD_SIZES.u128) * 2),
      )
      offset += FIELD_SIZES.u128

      const reservedBalance = parseU128LE(
        hex.slice(offset * 2, (offset + FIELD_SIZES.u128) * 2),
      )
      offset += FIELD_SIZES.u128

      const frozenBalance = parseU128LE(
        hex.slice(offset * 2, (offset + FIELD_SIZES.u128) * 2),
      )

      return {
        nonce,
        consumers,
        providers,
        sufficients,
        free: freeBalance.toString(),
        reserved: reservedBalance.toString(),
        frozen: frozenBalance.toString(),
      }
    }
    catch (fallbackError) {
      addLog(`手动解析账户数据也失败: ${fallbackError.message}`, 'error')
      return null
    }
  }
}

// 查询余额 (HTTP 方式)
async function queryBalance() {
  try {
    addLog(`查询账户余额: ${polkadotState.sourceAccount}`, 'info')

    if (!polkadotState.isConnected) {
      throw new Error('未连接到链')
    }

    // 获取最新区块哈希
    const latestBlockHash = await makeRpcRequest('chain_getHead')

    // 构建存储键
    const storageKey = getAccountStorageKey(polkadotState.sourceAccount)

    // 查询账户信息
    const accountData = await makeRpcRequest('state_getStorage', [
      storageKey,
      latestBlockHash,
    ])

    if (!accountData) {
      addLog('账户不存在或余额为 0', 'warning')
      polkadotState.balance = '0'
      return
    }

    // 解析账户数据
    const parsedData = parseAccountData(accountData)

    if (!parsedData) {
      addLog('无法解析账户数据', 'error')
      return
    }

    polkadotState.balance = parsedData.free
    const balanceFormatted = (Number(parsedData.free) / 1e12).toFixed(4)

    addLog(`余额: ${balanceFormatted} 个币`, 'success')
    addLog(`Nonce: ${parsedData.nonce}`, 'info')
  }
  catch (error) {
    addLog(`查询余额失败: ${error.message}`, 'error')
  }
}

// 执行转账 (HTTP 方式)
async function executeTransfer() {
  try {
    polkadotState.isTransferring = true
    polkadotState.transferStatus = null
    polkadotState.transferError = null

    const amount = polkadotState.transferAmount
    const amountFormatted = (Number(amount) / 1e12).toFixed(4)

    addLog(`开始转账: ${amountFormatted} 个币`, 'info')

    if (!polkadotState.isConnected) {
      throw new Error('未连接到链')
    }

    // 检查余额是否足够
    if (
      polkadotState.balance
      && Number(amount) > Number(polkadotState.balance)
    ) {
      throw new Error('余额不足')
    }

    // 获取账户 nonce（用于实际转账）
    const latestBlockHash = await makeRpcRequest('chain_getHead')
    const storageKey = getAccountStorageKey(polkadotState.sourceAccount)
    const accountData = await makeRpcRequest('state_getStorage', [
      storageKey,
      latestBlockHash,
    ])

    // 解析 nonce
    let nonce = 0
    if (accountData) {
      const parsedData = parseAccountData(accountData)
      if (parsedData) {
        nonce = parsedData.nonce
      }
    }

    // 模拟构建和签名交易
    addLog(`构建交易 (nonce: ${nonce})...`, 'info')
    await new Promise(resolve => setTimeout(resolve, 1000))

    addLog('签名交易...', 'info')
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 模拟提交交易
    addLog('提交交易...', 'info')
    await new Promise(resolve => setTimeout(resolve, 1000))

    const mockTxHash = `0x${Math.random().toString(16).substring(2, 66)}`

    polkadotState.transferStatus = 'success'
    addLog(`转账完成! 哈希: ${mockTxHash}`, 'success')

    // 重新查询余额
    setTimeout(() => {
      queryBalance()
    }, 1000)
  }
  catch (error) {
    polkadotState.transferError = error.message
    addLog(`转账失败: ${error.message}`, 'error')
  }
  finally {
    polkadotState.isTransferring = false
  }
}

// 清空日志
function clearLogs() {
  polkadotState.logs = []
  addLog('📝 日志已清空', 'info')
}

// 页面加载时自动连接
onMounted(() => {
  connectToChain()
})
</script>

<template>
  <div class="polkadot-demo">
    <!-- 标题区域 -->
    <div class="header-section">
      <h1 class="title">
        <i class="i-carbon-blockchain text-6xl" />
        Polkadot HTTP RPC Demo
      </h1>
      <p class="subtitle">
        使用 HTTP JSON-RPC 连接自定义链并执行余额查询和转账操作
      </p>

      <!-- 使用说明 -->
      <div class="usage-info">
        <h3>📋 使用说明</h3>
        <ul>
          <li>
            进入节点目录:
            <code>cd /Users/<USER>/solochain-template/target/release/</code>
          </li>
          <li>⚙️ 启动命令:</li>
        </ul>
        <div class="command-block">
          <code>
            ./solochain-template-node \<br>
            --dev \<br>
            --rpc-port 9933 \<br>
            --rpc-external \<br>
            --rpc-cors all \<br>
            --rpc-methods Unsafe
          </code>
        </div>
        <ul>
          <li>💰 查询账户余额</li>
          <li>🚀 演示转账操作</li>
          <li>🔧 动态计算存储键</li>
        </ul>
        <p class="note">
          <strong>注意：</strong> 使用 HTTP RPC，动态计算存储键，支持 SCALE 数据解析。
        </p>
      </div>
    </div>

    <!-- 连接状态卡片 -->
    <div class="status-card">
      <div class="status-header">
        <h2>
          <i class="i-carbon-network-3" />
          连接状态
        </h2>
        <div class="status-indicator">
          <div
            class="status-dot"
            :class="{
              connected: polkadotState.isConnected,
              connecting: polkadotState.isConnecting,
              error: polkadotState.connectionError,
            }"
          />
          <span class="status-text">
            {{
              polkadotState.isConnected
                ? '已连接'
                : polkadotState.isConnecting
                  ? '连接中...'
                  : polkadotState.connectionError
                    ? '连接失败'
                    : '未连接'
            }}
          </span>
        </div>
      </div>

      <div class="connection-info">
        <div class="info-item">
          <span class="label">RPC 地址:</span>
          <span class="value">{{ polkadotState.rpcUrl }}</span>
        </div>
        <div class="info-item">
          <span class="label">连接方式:</span>
          <span class="value">HTTP JSON-RPC</span>
        </div>
        <div class="info-item">
          <span class="label">查询账户:</span>
          <span class="value font-mono">{{ polkadotState.sourceAccount }}</span>
        </div>
        <div class="info-item">
          <span class="label">目标账户:</span>
          <span class="value font-mono">{{ polkadotState.targetAccount }}</span>
        </div>
      </div>

      <button
        v-if="!polkadotState.isConnected && !polkadotState.isConnecting"
        class="connect-btn"
        @click="connectToChain"
      >
        <i class="i-carbon-connect" />
        重新连接
      </button>
    </div>

    <!-- 余额查询卡片 -->
    <div v-if="polkadotState.isConnected" class="balance-card">
      <div class="card-header">
        <h2>
          <i class="i-carbon-wallet" />
          账户余额
        </h2>
        <button class="refresh-btn" @click="queryBalance">
          <i class="i-carbon-refresh" />
          刷新
        </button>
      </div>

      <div class="balance-display">
        <div
          v-if="polkadotState.balance !== null"
          class="balance-amount"
        >
          <span class="amount">{{ (Number(polkadotState.balance) / 1e12).toFixed(4) }}</span>
          <span class="unit">个币</span>
        </div>
        <div v-else class="balance-loading">
          <i class="i-carbon-circle-dash animate-spin" />
          查询中...
        </div>

        <div v-if="polkadotState.balance !== null" class="balance-raw">
          原始值: {{ polkadotState.balance }}
        </div>
      </div>
    </div>

    <!-- 转账操作卡片 -->
    <div v-if="polkadotState.isConnected" class="transfer-card">
      <div class="card-header">
        <h2>
          <i class="i-carbon-send" />
          转账操作
        </h2>
      </div>

      <div class="transfer-form">
        <div class="form-group">
          <label>转账金额 (最小单位)</label>
          <input
            v-model="polkadotState.transferAmount"
            type="text"
            class="amount-input"
            placeholder="*************"
          />
          <div class="amount-hint">
            约 {{ (Number(polkadotState.transferAmount || 0) / 1e12).toFixed(4) }} 个币
          </div>
        </div>

        <div class="form-group">
          <label>目标账户</label>
          <input
            v-model="polkadotState.targetAccount"
            type="text"
            class="address-input"
            readonly
          />
        </div>

        <button
          :disabled="polkadotState.isTransferring || !polkadotState.balance"
          class="transfer-btn"
          @click="executeTransfer"
        >
          <i
            v-if="polkadotState.isTransferring"
            class="i-carbon-circle-dash animate-spin"
          />
          <i v-else class="i-carbon-send" />
          {{ polkadotState.isTransferring ? '转账中...' : '执行转账' }}
        </button>

        <div
          v-if="polkadotState.transferStatus === 'success'"
          class="transfer-success"
        >
          <i class="i-carbon-checkmark-filled" />
          转账成功！
        </div>

        <div v-if="polkadotState.transferError" class="transfer-error">
          <i class="i-carbon-warning-filled" />
          {{ polkadotState.transferError }}
        </div>
      </div>
    </div>

    <!-- 日志区域 -->
    <div class="logs-section">
      <div class="logs-header">
        <h2>
          <i class="i-carbon-document" />
          操作日志
        </h2>
        <button class="clear-btn" @click="clearLogs">
          <i class="i-carbon-trash-can" />
          清空
        </button>
      </div>

      <div class="logs-container">
        <div
          v-for="log in polkadotState.logs"
          :key="log.id"
          class="log-item"
          :class="log.type"
        >
          <span class="log-time">{{ log.timestamp }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>

        <div v-if="polkadotState.logs.length === 0" class="no-logs">
          暂无日志记录
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.polkadot-demo {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 标题区域 */
.header-section {
  text-align: center;
  margin-bottom: 3rem;
}

.title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  font-size: 1.2rem;
  color: #6b7280;
  margin: 0;
}

.usage-info {
  max-width: 600px;
  margin: 2rem auto;
  padding: 1.5rem;
  background: #f8fafc;
  border-radius: 12px;
  border-left: 4px solid #3b82f6;
  text-align: left;
}

.usage-info h3 {
  margin: 0 0 1rem 0;
  color: #1f2937;
  font-size: 1.1rem;
}

.usage-info ul {
  margin: 0 0 1rem 0;
  padding-left: 1.5rem;
}

.usage-info li {
  margin-bottom: 0.5rem;
  color: #374151;
}

.usage-info code {
  background: #e5e7eb;
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 0.9rem;
}

.usage-info .note {
  margin: 1rem 0 0 0;
  padding: 1rem;
  background: #fef3cd;
  border-radius: 8px;
  color: #92400e;
  font-size: 0.9rem;
}

.command-block {
  margin: 0.5rem 0 1rem 0;
  padding: 1rem;
  background: #1f2937;
  border-radius: 8px;
  border-left: 4px solid #10b981;
}

.command-block code {
  color: #10b981;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 0.9rem;
  line-height: 1.6;
  background: none;
  padding: 0;
}

/* 卡片通用样式 */
.status-card,
.balance-card,
.transfer-card,
.logs-section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}

.status-card:hover,
.balance-card:hover,
.transfer-card:hover,
.logs-section:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

/* 状态卡片 */
.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.status-header h2 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  color: #1f2937;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #9ca3af;
  transition: all 0.3s ease;
}

.status-dot.connected {
  background: #10b981;
  box-shadow: 0 0 8px rgba(16, 185, 129, 0.4);
}

.status-dot.connecting {
  background: #f59e0b;
  animation: pulse 2s infinite;
}

.status-dot.error {
  background: #ef4444;
  box-shadow: 0 0 8px rgba(239, 68, 68, 0.4);
}

.status-text {
  font-weight: 500;
  color: #374151;
}

.connection-info {
  display: grid;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: #f9fafb;
  border-radius: 8px;
}

.label {
  font-weight: 500;
  color: #6b7280;
}

.value {
  color: #1f2937;
  font-weight: 500;
}

.connect-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.connect-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

/* 余额卡片 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.card-header h2 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  color: #1f2937;
}

.refresh-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #f3f4f6;
  color: #374151;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.refresh-btn:hover {
  background: #e5e7eb;
}

.balance-display {
  text-align: center;
  padding: 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
}

.balance-amount {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.amount {
  font-size: 3rem;
  font-weight: 700;
}

.unit {
  font-size: 1.2rem;
  opacity: 0.8;
}
