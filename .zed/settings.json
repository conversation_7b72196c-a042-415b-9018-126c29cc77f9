{"vim_mode": false, "ui_font_size": 16, "buffer_font_size": 14, "theme": {"mode": "system", "light": "One Light", "dark": "One Dark"}, "project_panel": {"dock": "left", "default_width": 240}, "outline_panel": {"dock": "right", "default_width": 300}, "collaboration_panel": {"dock": "left", "default_width": 240}, "chat_panel": {"dock": "right", "default_width": 240}, "notification_panel": {"dock": "right", "default_width": 240}, "terminal": {"dock": "bottom", "default_height": 320, "font_size": 14, "shell": {"program": "zsh"}}, "git": {"git_gutter": "tracked_files", "inline_blame": {"enabled": true}}, "formatter": "auto", "format_on_save": "on", "code_actions_on_format": {"source.fixAll.eslint": true, "source.organizeImports": true}, "lsp": {"typescript-language-server": {"initialization_options": {"preferences": {"includeInlayParameterNameHints": "all", "includeInlayParameterNameHintsWhenArgumentMatchesName": true, "includeInlayFunctionParameterTypeHints": true, "includeInlayVariableTypeHints": true, "includeInlayPropertyDeclarationTypeHints": true, "includeInlayFunctionLikeReturnTypeHints": true, "includeInlayEnumMemberValueHints": true}}}}, "languages": {"TypeScript": {"formatter": "prettier", "code_actions_on_format": {"source.fixAll.eslint": true, "source.organizeImports": true}}, "JavaScript": {"formatter": "prettier", "code_actions_on_format": {"source.fixAll.eslint": true, "source.organizeImports": true}}, "Vue": {"formatter": "prettier", "code_actions_on_format": {"source.fixAll.eslint": true, "source.organizeImports": true}}}, "auto_update": true, "telemetry": {"diagnostics": true, "metrics": false}}