[{"bindings": ["cmd-1"], "action": "workspace::ToggleLeftDock"}, {"bindings": ["cmd-2"], "action": "workspace::ToggleRightDock"}, {"bindings": ["cmd-3"], "action": "git::ToggleGitBlame"}, {"bindings": ["cmd-4"], "action": "terminal_panel::ToggleFocus"}, {"bindings": ["ctrl-f"], "action": "editor::<PERSON><PERSON>"}, {"bindings": ["shift-alt-f"], "action": "editor::FixDiagnostics"}, {"bindings": ["cmd-shift-e"], "action": "project_panel::RevealInProjectPanel"}, {"bindings": ["cmd-shift-r"], "action": "workspace::RevealInFinder"}, {"bindings": ["escape"], "action": "project_panel::CollapseAllEntries", "context": "ProjectPanel"}]