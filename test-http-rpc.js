#!/usr/bin/env node

/**
 * 测试 HTTP RPC 连接脚本
 * 用于验证本地 Substrate 节点的 HTTP RPC 是否正常工作
 */

const RPC_URL = 'http://127.0.0.1:9933';

// HTTP JSON-RPC 请求函数
async function makeRpcRequest(method, params = []) {
  const requestBody = {
    jsonrpc: '2.0',
    id: Math.floor(Math.random() * 1000),
    method,
    params
  };
  
  try {
    console.log(`🔄 发送请求: ${method}`);
    
    const response = await fetch(RPC_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody)
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    
    if (data.error) {
      throw new Error(`RPC Error ${data.error.code}: ${data.error.message}`);
    }
    
    console.log(`✅ ${method} 成功:`, data.result);
    return data.result;
  } catch (error) {
    console.error(`❌ ${method} 失败:`, error.message);
    throw error;
  }
}

// 测试基本连接
async function testConnection() {
  console.log('🚀 开始测试 HTTP RPC 连接...\n');
  
  try {
    // 测试链信息
    console.log('📋 测试基本信息:');
    const chainName = await makeRpcRequest('system_chain');
    const version = await makeRpcRequest('system_version');
    const nodeType = await makeRpcRequest('system_nodeType');
    
    console.log(`\n📊 节点信息:`);
    console.log(`  - 链名称: ${chainName}`);
    console.log(`  - 版本: ${version}`);
    console.log(`  - 节点类型: ${nodeType}`);
    
    // 测试区块信息
    console.log('\n🔗 测试区块信息:');
    const latestBlockHash = await makeRpcRequest('chain_getHead');
    const blockHeader = await makeRpcRequest('chain_getHeader', [latestBlockHash]);
    const genesisHash = await makeRpcRequest('chain_getBlockHash', [0]);
    
    console.log(`\n📦 区块信息:`);
    console.log(`  - 最新区块哈希: ${latestBlockHash}`);
    console.log(`  - 当前区块高度: #${parseInt(blockHeader.number, 16)}`);
    console.log(`  - 创世区块哈希: ${genesisHash}`);
    
    // 测试运行时信息
    console.log('\n⚙️  测试运行时信息:');
    const runtimeVersion = await makeRpcRequest('state_getRuntimeVersion');
    
    console.log(`\n🔧 运行时信息:`);
    console.log(`  - 规范名称: ${runtimeVersion.specName}`);
    console.log(`  - 规范版本: ${runtimeVersion.specVersion}`);
    console.log(`  - 实现名称: ${runtimeVersion.implName}`);
    console.log(`  - 实现版本: ${runtimeVersion.implVersion}`);
    
    console.log('\n✅ 所有测试通过！HTTP RPC 连接正常工作。');
    console.log('\n💡 现在你可以启动 Vue 应用并测试完整功能。');
    
  } catch (error) {
    console.error('\n❌ 测试失败:', error.message);
    console.log('\n🔧 故障排除建议:');
    console.log('1. 确保本地节点正在运行');
    console.log('2. 检查启动命令是否包含以下参数:');
    console.log('   --rpc-port 9933 --rpc-external --rpc-cors all --rpc-methods Unsafe');
    console.log('3. 检查端口 9933 是否被占用');
    console.log('4. 检查防火墙设置');
    
    process.exit(1);
  }
}

// 运行测试
testConnection();
