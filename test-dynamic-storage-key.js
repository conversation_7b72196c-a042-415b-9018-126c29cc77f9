#!/usr/bin/env node

/**
 * 测试动态存储键计算
 */

import { xxhashAsHex, blake2AsHex } from '@polkadot/util-crypto';
import { u8aToHex, stringToU8a } from '@polkadot/util';
import { decodeAddress } from '@polkadot/keyring';

// 动态计算存储前缀
const getStoragePrefix = (palletName, storageName) => {
  // 计算 pallet 名称的 xxhash128
  const palletHash = xxhashAsHex(stringToU8a(palletName), 128);
  // 计算 storage 名称的 xxhash128
  const storageHash = xxhashAsHex(stringToU8a(storageName), 128);
  // 组合前缀
  return palletHash + storageHash.slice(2); // 移除第二个哈希的 0x 前缀
};

// 动态计算账户存储键
const getAccountStorageKey = (accountAddress) => {
  try {
    // 动态计算 System.Account 的存储前缀
    const storagePrefix = getStoragePrefix('System', 'Account');
    
    // 解码账户地址为 32 字节的公钥
    const accountId = decodeAddress(accountAddress);
    
    // 计算账户 ID 的 Blake2_128 哈希（用于 Blake2_128Concat）
    const accountIdHash = blake2AsHex(accountId, 128);
    
    // 将账户 ID 转换为十六进制字符串
    const accountIdHex = u8aToHex(accountId);
    
    // Blake2_128Concat 格式：hash(key) + key
    const blake2_128Concat = accountIdHash + accountIdHex.slice(2);
    
    // 完整的存储键：存储前缀 + Blake2_128Concat(账户ID)
    const storageKey = storagePrefix + blake2_128Concat.slice(2);
    
    return storageKey;
  } catch (error) {
    console.error(`存储键计算失败: ${error.message}`);
    throw error;
  }
};

console.log('🧪 测试动态存储键计算...\n');

// 测试账户地址
const testAccount = '5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY';

console.log(`📋 测试账户: ${testAccount}`);

// 计算存储前缀
const storagePrefix = getStoragePrefix('System', 'Account');
console.log(`🔑 System.Account 存储前缀: ${storagePrefix}`);

// 计算完整存储键
const storageKey = getAccountStorageKey(testAccount);
console.log(`🔐 完整存储键: ${storageKey}`);

// 已知的正确存储键（从之前的测试中获得）
const knownStorageKey = '0x26aa394eea5630e07c48ae0c9558cef7b99d880ec681799c0cf30e8886371da9de1e86a9a8c739864cf3cc5ec2bea59fd43593c715fdd31c61141abd04a99fd6822c8558854ccde39a5684e7a56da27d';

console.log(`\n🔍 验证结果:`);
console.log(`计算的存储键: ${storageKey}`);
console.log(`已知存储键:   ${knownStorageKey}`);

if (storageKey === knownStorageKey) {
  console.log('✅ 存储键计算正确！');
} else {
  console.log('❌ 存储键计算不匹配');
  console.log('\n🔧 调试信息:');
  
  // 分解已知存储键
  const knownPrefix = knownStorageKey.slice(0, 66); // 前 32 字节
  const knownSuffix = knownStorageKey.slice(66);
  
  console.log(`已知前缀: ${knownPrefix}`);
  console.log(`计算前缀: ${storagePrefix}`);
  console.log(`已知后缀: ${knownSuffix}`);
  
  // 计算账户部分
  const accountId = decodeAddress(testAccount);
  const accountIdHash = blake2AsHex(accountId, 128);
  const accountIdHex = u8aToHex(accountId);
  const blake2_128Concat = accountIdHash + accountIdHex.slice(2);
  
  console.log(`账户ID: ${accountIdHex}`);
  console.log(`账户ID哈希: ${accountIdHash}`);
  console.log(`Blake2_128Concat: ${blake2_128Concat}`);
}

// 测试 HTTP RPC 调用
async function testRpcCall() {
  try {
    console.log('\n🌐 测试 HTTP RPC 调用...');
    
    const response = await fetch('http://127.0.0.1:9933', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        id: 1,
        method: 'state_getStorage',
        params: [storageKey]
      })
    });
    
    const data = await response.json();
    
    if (data.result) {
      console.log('✅ RPC 调用成功，账户数据存在');
      console.log(`📄 账户数据: ${data.result}`);
    } else if (data.result === null) {
      console.log('⚠️  RPC 调用成功，但账户数据为空');
    } else {
      console.log('❌ RPC 调用失败:', data.error);
    }
  } catch (error) {
    console.log('❌ RPC 调用失败:', error.message);
    console.log('💡 请确保本地节点正在运行');
  }
}

// 运行 RPC 测试
testRpcCall();
