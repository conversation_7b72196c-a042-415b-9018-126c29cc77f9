# Polkadot HTTP RPC Demo

这是一个使用纯 HTTP JSON-RPC 的 Vue.js 演示项目，展示了如何通过 HTTP 连接到自定义 Substrate 链并执行基本操作，完全不依赖 WebSocket。

## 功能特性

- 🔗 通过 HTTP JSON-RPC 连接到本地 Substrate 节点 (`http://127.0.0.1:9933`)
- 💰 使用 HTTP RPC 查询指定账户余额
- 🚀 演示 HTTP RPC 转账操作流程
- 📊 实时操作日志
- 🎨 Notion 风格的现代化界面
- ⚡ 完全不依赖 WebSocket，纯 HTTP 实现

## 技术栈

- **Vue 3** - 前端框架
- **HTTP JSON-RPC** - 区块链交互协议
- **Fetch API** - HTTP 请求
- **UnoCSS** - 原子化 CSS 框架
- **Vite** - 构建工具

## 快速开始

### 1. 安装依赖

```bash
pnpm install
```

### 2. 启动开发服务器

```bash
pnpm dev
```

### 3. 启动本地 Substrate 节点 (HTTP RPC)

**重要：** 使用以下命令启动支持 HTTP RPC 的节点：

```bash
./solochain-template-node \
--dev \
--rpc-port 9933 \
--rpc-external \
--rpc-cors all \
--rpc-methods Unsafe
```

参数说明：
- `--dev`: 开发模式
- `--rpc-port 9933`: HTTP RPC 端口
- `--rpc-external`: 允许外部连接
- `--rpc-cors all`: 允许所有 CORS 请求
- `--rpc-methods Unsafe`: 允许不安全的 RPC 方法（开发用）

### 4. 访问应用

打开浏览器访问 `http://localhost:3333`

## HTTP RPC 方法演示

本项目演示了以下 HTTP JSON-RPC 方法的使用：

### 基础信息查询
- `system_chain` - 获取链名称
- `system_version` - 获取节点版本
- `chain_getHead` - 获取最新区块哈希
- `chain_getHeader` - 获取区块头信息
- `chain_getBlockHash` - 获取指定高度的区块哈希

### 状态查询
- `state_getStorage` - 查询存储数据
- `state_getRuntimeVersion` - 获取运行时版本

### 交易相关
- `author_submitExtrinsic` - 提交交易（演示）

## 配置说明

### RPC 端点
- **HTTP RPC**: `http://127.0.0.1:9933`
- **查询账户**: `5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY`
- **目标账户**: `5HpG9w8EBLe5XCrbczpwq5TSXvedjrBGCwqxK1iQ7qUsSWFc`

### 转账金额
默认转账金额为 `1000000000000`（最小单位），相当于 1 个币（假设 12 位小数）。

## 重要说明

⚠️ **这是一个演示项目**

- 转账功能仅为流程演示，不会执行真实的链上交易
- 余额查询使用简化的存储键，实际应用需要正确的 SCALE 编码
- 实际转账需要私钥签名和正确的交易构建

## HTTP vs WebSocket

### HTTP RPC 优势
- ✅ 简单的请求/响应模式
- ✅ 更好的防火墙兼容性
- ✅ 易于调试和测试
- ✅ 无需维持长连接

### HTTP RPC 限制
- ❌ 无法实时订阅事件
- ❌ 每次请求都需要建立连接
- ❌ 无法监听区块更新

## 实际应用建议

对于生产环境，建议：

1. **使用正确的 SCALE 编码/解码**
```javascript
// 安装 @polkadot-api/substrate-bindings
import { blake2b } from "@noble/hashes/blake2b"
import { twoX128 } from "@polkadot-api/substrate-bindings"
```

2. **实现真实的交易签名**
```javascript
import { Keyring } from '@polkadot/keyring'

const keyring = new Keyring({ type: 'sr25519' })
const pair = keyring.addFromMnemonic('your mnemonic here')
```

3. **错误处理和重试机制**
```javascript
const makeRpcRequestWithRetry = async (method, params, maxRetries = 3) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await makeRpcRequest(method, params)
    } catch (error) {
      if (i === maxRetries - 1) throw error
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)))
    }
  }
}
```

## 故障排除

### 连接失败

如果连接失败，请检查：

1. 本地节点是否正在运行
2. 端口 9933 是否被占用
3. 是否使用了正确的启动参数
4. 防火墙设置是否阻止连接
5. CORS 设置是否正确

### 常见错误

- **CORS 错误**: 确保使用 `--rpc-cors all` 参数
- **连接拒绝**: 确保使用 `--rpc-external` 参数
- **方法不允许**: 确保使用 `--rpc-methods Unsafe` 参数

## 开发说明

### 项目结构

```
src/
├── pages/
│   └── index.vue          # 主要的 Demo 页面
├── main.ts               # 应用入口
└── ...
```

### 关键代码

- **HTTP RPC 请求**: `makeRpcRequest()` 函数
- **余额查询**: `queryBalance()` 函数  
- **转账演示**: `executeTransfer()` 函数
- **状态管理**: `polkadotState` 响应式对象

## 许可证

MIT License
