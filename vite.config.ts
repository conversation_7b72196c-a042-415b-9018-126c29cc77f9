import path from 'node:path'
import VueI18n from '@intlify/unplugin-vue-i18n/vite'

import vue from '@vitejs/plugin-vue'
import { codeInspectorPlugin } from 'code-inspector-plugin'
import Unocss from 'unocss/vite'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { VueRouterAutoImports } from 'unplugin-vue-router'
import VueRouter from 'unplugin-vue-router/vite'
import { defineConfig, loadEnv } from 'vite'
import VueDevTools from 'vite-plugin-vue-devtools'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  return {
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src'),
      },
    },
    plugins: [
      vue(),
      VueRouter({
        extensions: ['.vue'],
        dts: 'src/typed-router.d.ts',
      }),

      VueI18n({
        include: [path.resolve(__dirname, 'locales/**')],
      }),
      AutoImport({
        imports: [
          'vue',
          'pinia',
          'vue-i18n',
          '@vueuse/head',
          '@vueuse/core',
          VueRouterAutoImports,
          {
            // '@/api/allApi': ['walletApi', 'payApi'],
            'vue-router/auto': ['useLink'],
            'dayjs': [['default', 'dayjs']],
            'radash': [
              'throttle',
              'debounce',
              'clone',
              'tryit',
              'omit',
              'shake',
              'assign',
            ],
          },

        ],
        dts: 'src/auto-imports.d.ts',
        dirs: ['src/composables', 'src/stores'],
        vueTemplate: true,
        eslintrc: {
          enabled: true,
          filepath: './.eslintrc-auto-import.mjs',
          globalsPropValue: true,
        },
      }),
      Components({
        extensions: ['vue'],
        include: [/\.vue$/, /\.vue\?vue/],
        dts: 'src/components.d.ts',
      }),
      Unocss(),
      codeInspectorPlugin({
        bundler: 'vite',
        hideConsole: true,
      }),
      VueDevTools(),
    ],
    server: {
      proxy: {
        '^/api': {
          target: 'https://xx.cn',
          changeOrigin: true,
          // rewrite: (path) => path.replace(/^\/api/, ''),
        },
      },
      watch: {
        ignored: ['**/node_modules/**', '**/.git/**', '**/sdk/**', '**/dist/**'],
      },
    },
  }
})
